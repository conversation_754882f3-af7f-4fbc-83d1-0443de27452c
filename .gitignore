# ModernAction.io - Root .gitignore

# Environment variables and secrets
.env
.env.local
.env.development
.env.test
.env.production
.env.*
*.env

# AWS credentials and config
.aws/
aws-credentials.json
aws-config.json

# Database files
*.db
*.sqlite
*.sqlite3
modernaction.db

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.coverage
.pytest_cache/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
package-lock.json
yarn.lock

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs
logs/
*.log
log/

# CDK
cdk.out/
cdk.context.json

# Lambda deployment packages
apps/lambda/*/build/
apps/lambda/*/*.zip
lambda-package.zip

# Cache directories
.pytest_cache/
__pycache__/
.cache/

# Build artifacts
build/
dist/
*.egg-info/

# Temporary files
tmp/
temp/
.tmp/
.temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
*.old

# API keys and secrets (additional patterns)
*secret*
*key*
*token*
*password*
*credentials*
secrets.json
config.json

# Test artifacts
test-results/
playwright-report/
coverage/

# Build artifacts
*.tar.gz
*.zip
*.rar
