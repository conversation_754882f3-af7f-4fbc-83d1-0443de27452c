"""Add action model and relationships

Revision ID: 002
Revises: 001
Create Date: 2024-07-17 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade():
    # Create enums for action types and status
    action_status = postgresql.ENUM('PENDING', 'SENT', 'DELIVERED', 'FAILED', 'BOUNCED', name='actionstatus')
    action_status.create(op.get_bind(), checkfirst=True)

    action_type = postgresql.ENUM('EMAIL', 'PHONE', 'LETTER', 'SOCIAL_MEDIA', 'PETITION', name='actiontype')
    action_type.create(op.get_bind(), checkfirst=True)

    # Update existing actions table to use enums instead of strings
    # First, update any existing data to use valid enum values
    op.execute("""
        UPDATE actions
        SET action_type = CASE
            WHEN LOWER(action_type) IN ('email', 'e-mail') THEN 'EMAIL'
            WHEN LOWER(action_type) IN ('phone', 'call') THEN 'PHONE'
            WHEN LOWER(action_type) IN ('letter', 'mail') THEN 'LETTER'
            WHEN LOWER(action_type) IN ('social_media', 'social', 'twitter', 'facebook') THEN 'SOCIAL_MEDIA'
            WHEN LOWER(action_type) IN ('petition', 'sign') THEN 'PETITION'
            ELSE 'EMAIL'
        END
    """)

    op.execute("""
        UPDATE actions
        SET status = CASE
            WHEN LOWER(status) IN ('pending', 'queued') THEN 'PENDING'
            WHEN LOWER(status) IN ('sent', 'sending') THEN 'SENT'
            WHEN LOWER(status) IN ('delivered', 'success') THEN 'DELIVERED'
            WHEN LOWER(status) IN ('failed', 'error') THEN 'FAILED'
            WHEN LOWER(status) IN ('bounced', 'bounce') THEN 'BOUNCED'
            ELSE 'PENDING'
        END
    """)

    # Convert the columns to use enums
    op.alter_column('actions', 'action_type',
                    type_=action_type,
                    postgresql_using='action_type::actiontype')

    op.alter_column('actions', 'status',
                    type_=action_status,
                    postgresql_using='status::actionstatus')

    # Add any missing columns that weren't in the original migration
    # The original migration has basic columns, we need to add the extended ones

    # Add new columns that are in the Action model but not in the original migration
    columns_to_add = [
        ('response_received_at', sa.DateTime(), True),
        ('contact_email', sa.String(), True),
        ('contact_phone', sa.String(), True),
        ('contact_address', sa.Text(), True),
        ('user_name', sa.String(), False),
        ('user_email', sa.String(), False),
        ('user_address', sa.Text(), True),
        ('user_zip_code', sa.String(), True),
        ('delivery_method', sa.String(), True),
        ('delivery_id', sa.String(), True),
        ('error_message', sa.Text(), True),
        ('retry_count', sa.Integer(), False),
        ('action_metadata', sa.JSON(), True),
    ]

    for column_name, column_type, nullable in columns_to_add:
        try:
            if nullable:
                op.add_column('actions', sa.Column(column_name, column_type, nullable=True))
            else:
                # For non-nullable columns, add with a default value first
                if column_name == 'user_name':
                    op.add_column('actions', sa.Column(column_name, column_type, nullable=False, server_default='Unknown'))
                elif column_name == 'user_email':
                    op.add_column('actions', sa.Column(column_name, column_type, nullable=False, server_default='<EMAIL>'))
                elif column_name == 'retry_count':
                    op.add_column('actions', sa.Column(column_name, column_type, nullable=False, server_default='0'))
                else:
                    op.add_column('actions', sa.Column(column_name, column_type, nullable=False))
        except Exception:
            # Column already exists, skip
            pass
    
    # Create indexes
    op.create_index(op.f('ix_actions_created_at'), 'actions', ['created_at'], unique=False)
    op.create_index(op.f('ix_actions_status'), 'actions', ['status'], unique=False)
    op.create_index(op.f('ix_actions_campaign_id'), 'actions', ['campaign_id'], unique=False)
    op.create_index(op.f('ix_actions_user_id'), 'actions', ['user_id'], unique=False)
    op.create_index(op.f('ix_actions_official_id'), 'actions', ['official_id'], unique=False)


def downgrade():
    # Drop indexes
    op.drop_index(op.f('ix_actions_official_id'), table_name='actions')
    op.drop_index(op.f('ix_actions_user_id'), table_name='actions')
    op.drop_index(op.f('ix_actions_campaign_id'), table_name='actions')
    op.drop_index(op.f('ix_actions_status'), table_name='actions')
    op.drop_index(op.f('ix_actions_created_at'), table_name='actions')
    
    # Drop actions table
    op.drop_table('actions')
    
    # Drop enums
    op.execute('DROP TYPE actiontype')
    op.execute('DROP TYPE actionstatus')