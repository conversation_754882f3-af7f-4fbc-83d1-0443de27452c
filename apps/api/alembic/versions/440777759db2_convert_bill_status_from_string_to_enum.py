"""Convert bill status from string to enum

Revision ID: 440777759db2
Revises: 003
Create Date: 2025-07-18 05:42:14.379446

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = '440777759db2'
down_revision: Union[str, Sequence[str], None] = '002'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Convert bill status from string to enum."""
    # Create the BillStatus enum
    bill_status_enum = postgresql.ENUM(
        'DRAFT', 'INTRODUCED', 'COMMITTEE', 'FLOOR', 'PASSED', 'SIGNED', 'VETOED', 'FAILED',
        name='billstatus'
    )
    bill_status_enum.create(op.get_bind(), checkfirst=True)

    # Convert the status column from string to enum
    # First, update any existing data to use valid enum values
    op.execute("""
        UPDATE bills
        SET status = CASE
            WHEN LOWER(status) IN ('draft') THEN 'DRAFT'
            WHEN LOWER(status) IN ('introduced', 'intro') THEN 'INTRODUCED'
            WHEN LOWER(status) IN ('committee', 'comm') THEN 'COMMITTEE'
            WHEN LOWER(status) IN ('floor') THEN 'FLOOR'
            WHEN LOWER(status) IN ('passed', 'pass') THEN 'PASSED'
            WHEN LOWER(status) IN ('signed', 'enacted') THEN 'SIGNED'
            WHEN LOWER(status) IN ('vetoed', 'veto') THEN 'VETOED'
            WHEN LOWER(status) IN ('failed', 'fail', 'dead') THEN 'FAILED'
            ELSE 'INTRODUCED'
        END
    """)

    # Alter the column type
    op.alter_column('bills', 'status',
                    type_=bill_status_enum,
                    postgresql_using='status::billstatus')


def downgrade() -> None:
    """Convert bill status from enum back to string."""
    # Convert the status column back to string
    op.alter_column('bills', 'status',
                    type_=sa.String(),
                    postgresql_using='status::text')

    # Drop the enum type
    op.execute('DROP TYPE billstatus')
