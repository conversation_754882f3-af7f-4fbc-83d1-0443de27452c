# tests/test_bill_status_update.py
"""
Tests for bill status update functionality.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from sqlalchemy.orm import Session

from app.services.bill_status_update import BillStatusUpdateService
from app.models.bill import Bill, <PERSON><PERSON>tat<PERSON>, BillStatusPipeline


class TestBillStatusUpdateService:
    """Test cases for BillStatusUpdateService"""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def service(self, mock_db):
        """Create service instance with mocked database"""
        return BillStatusUpdateService(mock_db)
    
    @pytest.fixture
    def sample_bill(self):
        """Sample bill for testing"""
        bill = Mock(spec=Bill)
        bill.id = "test-bill-id"
        bill.title = "Test Bill"
        bill.status = BillStatus.INTRODUCED
        bill.openstates_id = "test-openstates-id"
        return bill
    
    @pytest.fixture
    def sample_openstates_response(self):
        """Sample OpenStates API response"""
        return {
            "id": "test-openstates-id",
            "title": "Test Bill",
            "status": "passed",
            "actions": [
                {
                    "date": "2024-01-01T00:00:00Z",
                    "description": "Introduced in House",
                    "organization": {"name": "House"}
                },
                {
                    "date": "2024-01-15T00:00:00Z",
                    "description": "Passed House floor vote",
                    "organization": {"name": "House"},
                    "result": "pass"
                }
            ]
        }
    
    def test_get_active_bills(self, service, mock_db):
        """Test getting active bills from database"""
        # Mock database query
        mock_bills = [Mock(spec=Bill) for _ in range(3)]
        mock_db.query.return_value.filter.return_value.filter.return_value.all.return_value = mock_bills
        
        result = service.get_active_bills()
        
        assert len(result) == 3
        mock_db.query.assert_called_once_with(Bill)
    
    @patch('app.services.bill_status_update.requests.get')
    def test_fetch_bill_status_from_openstates_success(self, mock_get, service, sample_openstates_response):
        """Test successful API call to OpenStates"""
        # Mock successful API response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = sample_openstates_response
        mock_get.return_value = mock_response
        
        with patch('app.services.bill_status_update.settings.OPEN_STATES_API_KEY', 'test-key'):
            result = service.fetch_bill_status_from_openstates("test-id")
        
        assert result == sample_openstates_response
        mock_get.assert_called_once()
    
    @patch('app.services.bill_status_update.requests.get')
    def test_fetch_bill_status_from_openstates_failure(self, mock_get, service):
        """Test API call failure"""
        # Mock failed API response
        mock_get.side_effect = Exception("API Error")
        
        with patch('app.services.bill_status_update.settings.OPEN_STATES_API_KEY', 'test-key'):
            result = service.fetch_bill_status_from_openstates("test-id")
        
        assert result is None
    
    def test_map_openstates_status_to_bill_status(self, service, sample_openstates_response):
        """Test status mapping from OpenStates to our enum"""
        # Test passed status
        result = service.map_openstates_status_to_bill_status(sample_openstates_response)
        assert result == BillStatus.FLOOR  # Based on "Passed House floor vote"
        
        # Test signed status
        signed_response = {
            "actions": [
                {"description": "Signed by Governor", "date": "2024-01-20T00:00:00Z"}
            ]
        }
        result = service.map_openstates_status_to_bill_status(signed_response)
        assert result == BillStatus.SIGNED
        
        # Test failed status
        failed_response = {
            "actions": [
                {"description": "Failed committee vote", "date": "2024-01-20T00:00:00Z"}
            ]
        }
        result = service.map_openstates_status_to_bill_status(failed_response)
        assert result == BillStatus.FAILED
    
    def test_is_significant_status_change(self, service):
        """Test significant status change detection"""
        # Test progression
        assert service.is_significant_status_change(BillStatus.INTRODUCED, BillStatus.COMMITTEE) == True
        assert service.is_significant_status_change(BillStatus.COMMITTEE, BillStatus.FLOOR) == True
        
        # Test final statuses
        assert service.is_significant_status_change(BillStatus.FLOOR, BillStatus.PASSED) == True
        assert service.is_significant_status_change(BillStatus.FLOOR, BillStatus.SIGNED) == True
        
        # Test no change
        assert service.is_significant_status_change(BillStatus.COMMITTEE, BillStatus.COMMITTEE) == False
        
        # Test regression (should not be significant in normal flow)
        assert service.is_significant_status_change(BillStatus.FLOOR, BillStatus.COMMITTEE) == False
    
    def test_create_status_change_record(self, service, mock_db, sample_bill, sample_openstates_response):
        """Test creating status change record"""
        new_status = BillStatus.PASSED
        status_changed_at = datetime.utcnow()
        original_status = sample_bill.status  # Store original status before it changes

        result = service.create_status_change_record(
            bill=sample_bill,
            new_status=new_status,
            external_data=sample_openstates_response,
            status_changed_at=status_changed_at
        )

        # Verify record creation
        assert isinstance(result, BillStatusPipeline)
        assert result.bill_id == sample_bill.id
        assert result.current_status == new_status
        assert result.previous_status == original_status  # Use stored original status

        # Verify database operations
        mock_db.add.assert_called_once_with(result)
        assert sample_bill.status == new_status
    
    @patch.object(BillStatusUpdateService, 'fetch_bill_status_from_openstates')
    @patch.object(BillStatusUpdateService, 'map_openstates_status_to_bill_status')
    @patch.object(BillStatusUpdateService, 'create_status_change_record')
    def test_update_bill_status_with_change(self, mock_create_record, mock_map_status, 
                                          mock_fetch_status, service, sample_bill, sample_openstates_response):
        """Test updating bill status when status has changed"""
        # Setup mocks
        mock_fetch_status.return_value = sample_openstates_response
        mock_map_status.return_value = BillStatus.PASSED
        mock_record = Mock(spec=BillStatusPipeline)
        mock_create_record.return_value = mock_record
        
        result = service.update_bill_status(sample_bill)
        
        # Verify the flow
        mock_fetch_status.assert_called_once_with(sample_bill.openstates_id)
        mock_map_status.assert_called_once_with(sample_openstates_response)
        mock_create_record.assert_called_once()
        assert result == mock_record
    
    @patch.object(BillStatusUpdateService, 'fetch_bill_status_from_openstates')
    @patch.object(BillStatusUpdateService, 'map_openstates_status_to_bill_status')
    def test_update_bill_status_no_change(self, mock_map_status, mock_fetch_status, 
                                        service, sample_bill, sample_openstates_response):
        """Test updating bill status when status hasn't changed"""
        # Setup mocks - same status
        mock_fetch_status.return_value = sample_openstates_response
        mock_map_status.return_value = BillStatus.INTRODUCED  # Same as current
        
        result = service.update_bill_status(sample_bill)
        
        # Should return None when no change
        assert result is None
    
    @patch.object(BillStatusUpdateService, 'get_active_bills')
    @patch.object(BillStatusUpdateService, 'update_bill_status')
    def test_update_all_active_bills(self, mock_update_bill, mock_get_bills, service, mock_db):
        """Test updating all active bills"""
        # Setup mocks
        mock_bills = [Mock(spec=Bill) for _ in range(3)]
        mock_get_bills.return_value = mock_bills
        
        # Mock some bills updating, some not
        mock_update_bill.side_effect = [Mock(), None, Mock()]  # 2 updated, 1 no change
        
        total_checked, total_updated = service.update_all_active_bills()
        
        assert total_checked == 3
        assert total_updated == 2
        assert mock_update_bill.call_count == 3
        assert mock_db.commit.call_count == 2  # Only for successful updates
