"""
Integration tests for the Bill Status Update Lambda function.

These tests verify the Lambda handler works correctly with mocked dependencies.
"""

import json
import os
import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Mock the imports before importing the handler
with patch.dict('sys.modules', {
    'shared.database': MagicMock(),
    'shared.bill_status_service': MagicMock()
}):
    from handler import lambda_handler


class TestLambdaHandler:
    """Test cases for the Lambda handler function"""
    
    @pytest.fixture
    def lambda_event(self):
        """Sample Lambda event from EventBridge schedule"""
        return {
            'source': 'aws.events',
            'detail-type': 'Scheduled Event',
            'detail': {}
        }
    
    @pytest.fixture
    def lambda_context(self):
        """Mock Lambda context"""
        context = Mock()
        context.get_remaining_time_in_millis.return_value = 300000  # 5 minutes
        return context
    
    @patch('handler.get_database_session')
    @patch('handler.BillStatusUpdateService')
    def test_lambda_handler_success(self, mock_service_class, mock_get_db, lambda_event, lambda_context):
        """Test successful Lambda execution"""
        # Setup mocks
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_service = Mock()
        mock_service.update_all_active_bills.return_value = (10, 3)  # 10 checked, 3 updated
        mock_service_class.return_value = mock_service
        
        # Execute handler
        result = lambda_handler(lambda_event, lambda_context)
        
        # Verify result
        assert result['statusCode'] == 200
        assert result['body']['total_checked'] == 10
        assert result['body']['total_updated'] == 3
        assert 'Bill status update completed successfully' in result['body']['message']
        
        # Verify service was called correctly
        mock_service_class.assert_called_once_with(mock_db)
        mock_service.update_all_active_bills.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.close.assert_called_once()
    
    @patch('handler.get_database_session')
    def test_lambda_handler_database_connection_failure(self, mock_get_db, lambda_event, lambda_context):
        """Test Lambda execution with database connection failure"""
        # Setup mock to return None (connection failure)
        mock_get_db.return_value = None
        
        # Execute handler
        result = lambda_handler(lambda_event, lambda_context)
        
        # Verify error response
        assert result['statusCode'] == 500
        assert 'Bill status update failed' in result['body']['message']
        assert 'Could not establish database connection' in result['body']['error']
    
    @patch('handler.get_database_session')
    @patch('handler.BillStatusUpdateService')
    def test_lambda_handler_service_exception(self, mock_service_class, mock_get_db, lambda_event, lambda_context):
        """Test Lambda execution with service exception"""
        # Setup mocks
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock service to raise exception
        mock_service_class.side_effect = Exception("Service error")
        
        # Execute handler
        result = lambda_handler(lambda_event, lambda_context)
        
        # Verify error response
        assert result['statusCode'] == 500
        assert 'Bill status update failed' in result['body']['message']
        assert 'Service error' in result['body']['error']
        
        # Verify database cleanup
        mock_db.close.assert_called_once()
    
    @patch('handler.get_database_session')
    @patch('handler.BillStatusUpdateService')
    def test_lambda_handler_partial_success(self, mock_service_class, mock_get_db, lambda_event, lambda_context):
        """Test Lambda execution with partial success (some bills fail to update)"""
        # Setup mocks
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_service = Mock()
        mock_service.update_all_active_bills.return_value = (20, 15)  # 20 checked, 15 updated (5 failed)
        mock_service_class.return_value = mock_service
        
        # Execute handler
        result = lambda_handler(lambda_event, lambda_context)
        
        # Verify result shows partial success
        assert result['statusCode'] == 200
        assert result['body']['total_checked'] == 20
        assert result['body']['total_updated'] == 15
        
        # Service should handle individual failures gracefully
        mock_service.update_all_active_bills.assert_called_once()
    
    def test_lambda_handler_missing_environment_variables(self, lambda_event, lambda_context):
        """Test Lambda execution with missing environment variables"""
        # Clear environment variables that might be set
        with patch.dict(os.environ, {}, clear=True):
            # This should be handled by the service initialization
            # The handler itself doesn't directly check env vars
            pass
    
    @patch('handler.get_database_session')
    @patch('handler.BillStatusUpdateService')
    def test_lambda_handler_no_bills_to_update(self, mock_service_class, mock_get_db, lambda_event, lambda_context):
        """Test Lambda execution when no bills need updating"""
        # Setup mocks
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_service = Mock()
        mock_service.update_all_active_bills.return_value = (0, 0)  # No bills found
        mock_service_class.return_value = mock_service
        
        # Execute handler
        result = lambda_handler(lambda_event, lambda_context)
        
        # Verify successful completion even with no updates
        assert result['statusCode'] == 200
        assert result['body']['total_checked'] == 0
        assert result['body']['total_updated'] == 0


class TestLambdaEnvironment:
    """Test Lambda environment setup and configuration"""
    
    def test_required_environment_variables(self):
        """Test that required environment variables are documented"""
        # This test documents the required environment variables
        required_vars = [
            'OPEN_STATES_API_KEY',
            'DATABASE_URL',  # Or individual DB components
        ]
        
        optional_vars = [
            'DB_HOST',
            'DB_PORT', 
            'DB_NAME',
            'DB_USER',
            'DB_PASSWORD'
        ]
        
        # In a real deployment, these would be set
        # This test serves as documentation
        assert len(required_vars) > 0
        assert len(optional_vars) > 0
    
    def test_lambda_timeout_configuration(self):
        """Test Lambda timeout is appropriate for the workload"""
        # Bill status updates can take time with many bills
        # Recommended timeout: 15 minutes (900 seconds)
        recommended_timeout = 900
        assert recommended_timeout >= 300  # At least 5 minutes
    
    def test_lambda_memory_configuration(self):
        """Test Lambda memory is appropriate for the workload"""
        # Database operations and API calls need reasonable memory
        # Recommended memory: 512 MB
        recommended_memory = 512
        assert recommended_memory >= 256  # At least 256 MB


if __name__ == "__main__":
    # Run tests locally
    pytest.main([__file__, "-v"])
