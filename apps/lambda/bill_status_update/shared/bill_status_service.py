"""
Simplified bill status update service for Lambda functions.

This is a Lambda-optimized version of the bill status update service
with minimal dependencies and optimized for serverless execution.
"""

import logging
import os
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import requests
from sqlalchemy.orm import Session
from sqlalchemy import text

logger = logging.getLogger(__name__)


class BillStatusUpdateService:
    """Simplified service for updating bill statuses in Lambda environment"""
    
    def __init__(self, db: Session):
        self.db = db
        self.api_key = os.getenv('OPEN_STATES_API_KEY')
        if not self.api_key:
            raise ValueError("OPEN_STATES_API_KEY environment variable is required")
    
    def get_active_bills(self) -> List[Dict[str, Any]]:
        """
        Get all bills that need status checking.
        
        Returns:
            List of bill dictionaries
        """
        query = text("""
            SELECT id, title, bill_number, status, openstates_id, last_action_date
            FROM bills 
            WHERE status IN ('draft', 'introduced', 'committee', 'floor')
            AND openstates_id IS NOT NULL
            ORDER BY last_action_date DESC NULLS LAST
        """)
        
        result = self.db.execute(query)
        bills = [dict(row._mapping) for row in result]
        
        logger.info(f"Found {len(bills)} active bills to check")
        return bills
    
    def fetch_bill_status_from_openstates(self, openstates_id: str) -> Optional[Dict[str, Any]]:
        """
        Fetch current bill status from OpenStates API.
        
        Args:
            openstates_id: OpenStates bill ID
            
        Returns:
            Dict containing bill data or None if not found
        """
        api_url = f"https://v3.openstates.org/bills/{openstates_id}"
        headers = {
            "X-API-KEY": self.api_key,
            "Accept": "application/json"
        }
        
        try:
            response = requests.get(api_url, headers=headers, timeout=30)
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch bill {openstates_id}: {e}")
            return None
    
    def map_openstates_status_to_bill_status(self, openstates_data: Dict[str, Any]) -> str:
        """
        Map OpenStates status to our bill status.
        
        Args:
            openstates_data: Raw data from OpenStates API
            
        Returns:
            Bill status string
        """
        actions = openstates_data.get('actions', [])
        
        # Check for final statuses first
        for action in actions:
            description = action.get('description', '').lower()
            if 'signed' in description:
                return 'signed'
            elif 'vetoed' in description:
                return 'vetoed'
            elif 'passed' in description and 'final' in description:
                return 'passed'
            elif 'failed' in description or 'defeated' in description:
                return 'failed'
        
        # Check for intermediate statuses
        if actions:
            latest_description = actions[-1].get('description', '').lower()
            if 'floor' in latest_description or 'third reading' in latest_description:
                return 'floor'
            elif 'committee' in latest_description:
                return 'committee'
        
        return 'introduced'
    
    def is_significant_status_change(self, old_status: str, new_status: str) -> bool:
        """
        Determine if a status change is significant.
        
        Args:
            old_status: Previous bill status
            new_status: New bill status
            
        Returns:
            True if the change is significant
        """
        status_order = {
            'draft': 0,
            'introduced': 1,
            'committee': 2,
            'floor': 3,
            'passed': 4,
            'signed': 5,
            'vetoed': 5,
            'failed': 5
        }
        
        old_order = status_order.get(old_status, 0)
        new_order = status_order.get(new_status, 0)
        
        return (new_order > old_order) or new_status in ['passed', 'signed', 'vetoed', 'failed']
    
    def update_bill_status(self, bill: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Update a single bill's status.
        
        Args:
            bill: Bill dictionary
            
        Returns:
            Status change record if updated, None otherwise
        """
        openstates_id = bill['openstates_id']
        if not openstates_id:
            return None
        
        # Fetch current status from API
        external_data = self.fetch_bill_status_from_openstates(openstates_id)
        if not external_data:
            return None
        
        # Determine new status
        new_status = self.map_openstates_status_to_bill_status(external_data)
        old_status = bill['status']
        
        # Check if status has changed
        if new_status == old_status:
            return None
        
        # Determine when the status changed
        actions = external_data.get('actions', [])
        status_changed_at = datetime.utcnow()
        if actions:
            latest_action_date = actions[-1].get('date')
            if latest_action_date:
                try:
                    status_changed_at = datetime.fromisoformat(latest_action_date.replace('Z', '+00:00'))
                except (ValueError, AttributeError):
                    pass
        
        is_significant = self.is_significant_status_change(old_status, new_status)
        
        # Extract vote details
        vote_details = None
        for action in reversed(actions):
            if any(keyword in action.get('description', '').lower() for keyword in ['vote', 'passed', 'failed']):
                vote_details = {
                    'action_description': action.get('description'),
                    'action_date': action.get('date'),
                    'organization': action.get('organization', {}).get('name'),
                    'result': action.get('result')
                }
                break
        
        # Create status change record
        insert_query = text("""
            INSERT INTO bill_status_pipeline 
            (id, created_at, updated_at, bill_id, previous_status, current_status, 
             status_changed_at, detected_at, external_data, vote_details, 
             notification_sent, is_significant_change, notes)
            VALUES (gen_random_uuid(), NOW(), NOW(), :bill_id, :previous_status, :current_status,
                    :status_changed_at, NOW(), :external_data, :vote_details,
                    false, :is_significant_change, :notes)
        """)
        
        self.db.execute(insert_query, {
            'bill_id': bill['id'],
            'previous_status': old_status,
            'current_status': new_status,
            'status_changed_at': status_changed_at,
            'external_data': external_data,
            'vote_details': vote_details,
            'is_significant_change': is_significant,
            'notes': f"Status updated from {old_status} to {new_status}"
        })
        
        # Update the bill's current status
        update_query = text("""
            UPDATE bills 
            SET status = :new_status, last_action_date = :status_changed_at, updated_at = NOW()
            WHERE id = :bill_id
        """)
        
        self.db.execute(update_query, {
            'new_status': new_status,
            'status_changed_at': status_changed_at,
            'bill_id': bill['id']
        })
        
        logger.info(f"Updated bill {bill['id']}: {old_status} -> {new_status}")
        
        return {
            'bill_id': bill['id'],
            'previous_status': old_status,
            'current_status': new_status,
            'is_significant_change': is_significant,
            'vote_details': vote_details
        }
    
    def update_all_active_bills(self) -> Tuple[int, int]:
        """
        Update status for all active bills.
        
        Returns:
            Tuple of (total_checked, total_updated)
        """
        active_bills = self.get_active_bills()
        total_checked = len(active_bills)
        total_updated = 0
        
        for bill in active_bills:
            try:
                status_record = self.update_bill_status(bill)
                if status_record:
                    total_updated += 1
                
            except Exception as e:
                logger.error(f"Error updating bill {bill['id']}: {e}")
                continue
        
        logger.info(f"Bill status update complete: {total_updated}/{total_checked} bills updated")
        return total_checked, total_updated
