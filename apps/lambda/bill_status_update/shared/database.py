"""
Database connection utilities for Lambda functions.

This module provides database connection functionality optimized for AWS Lambda,
including connection pooling and environment-based configuration.
"""

import os
import logging
from typing import Optional
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import NullPool

logger = logging.getLogger(__name__)

# Global variables for connection reuse
_engine = None
_SessionLocal = None


def get_database_url() -> str:
    """
    Get database URL from environment variables.
    
    Returns:
        Database connection URL
    """
    # Try to get from environment variables
    database_url = os.getenv('DATABASE_URL')
    if database_url:
        return database_url
    
    # Construct from individual components
    db_host = os.getenv('DB_HOST', 'localhost')
    db_port = os.getenv('DB_PORT', '5432')
    db_name = os.getenv('DB_NAME', 'modernaction')
    db_user = os.getenv('DB_USER', 'postgres')
    db_password = os.getenv('DB_PASSWORD', 'password')
    
    return f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"


def create_database_engine():
    """
    Create SQLAlchemy engine optimized for Lambda.
    
    Returns:
        SQLAlchemy engine
    """
    database_url = get_database_url()
    
    # Lambda-optimized engine configuration
    engine = create_engine(
        database_url,
        poolclass=NullPool,  # No connection pooling in Lambda
        pool_pre_ping=True,  # Verify connections before use
        pool_recycle=3600,   # Recycle connections after 1 hour
        echo=False,          # Set to True for SQL debugging
        connect_args={
            "connect_timeout": 10,
            "application_name": "modernaction-lambda"
        }
    )
    
    logger.info("Database engine created successfully")
    return engine


def get_database_session() -> Optional[Session]:
    """
    Get database session for Lambda function.
    
    Returns:
        SQLAlchemy session or None if connection fails
    """
    global _engine, _SessionLocal
    
    try:
        # Create engine if not exists
        if _engine is None:
            _engine = create_database_engine()
            _SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=_engine)
        
        # Create and return session
        session = _SessionLocal()
        logger.debug("Database session created successfully")
        return session
        
    except Exception as e:
        logger.error(f"Failed to create database session: {e}")
        return None


def test_database_connection() -> bool:
    """
    Test database connection.
    
    Returns:
        True if connection successful, False otherwise
    """
    try:
        db = get_database_session()
        if not db:
            return False
        
        # Simple query to test connection
        result = db.execute("SELECT 1").fetchone()
        db.close()
        
        logger.info("Database connection test successful")
        return result is not None
        
    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        return False
